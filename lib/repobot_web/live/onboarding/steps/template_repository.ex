defmodule RepobotWeb.Live.Onboarding.Steps.TemplateRepository do
  use RepobotWeb.Live.Onboarding.Step

  alias __MODULE__
  alias Repobot.Repositories

  def update(%{repository_created: repo_data} = assigns, socket) do
    # Handle repository creation events and forward to the create component
    if socket.assigns.selection_mode == :create do
      send_update(TemplateRepository.Create,
        id: "template-repository-create",
        repository_created: repo_data
      )
    end

    {:ok, assign(socket, assigns)}
  end

  def update(%{repository_loading_progress: {progress, message}} = assigns, socket) do
    # Handle repository loading progress updates
    {:ok,
     socket
     |> assign(assigns)
     |> assign(:loading_progress, progress)
     |> assign(:loading_message, message)}
  end

  def update(%{repository_loading_complete: result} = assigns, socket) do
    # Handle repository loading completion
    case result do
      %{"status" => "ok", "repositories_count" => _count, "repository_ids" => repository_ids} ->
        # Fetch repositories by IDs
        repositories = Repobot.Repositories.list_repositories_by_ids(repository_ids)

        {:ok,
         socket
         |> assign(assigns)
         |> assign(:repositories, repositories)
         |> assign(:loading_repositories, false)
         |> assign(:loading_progress, 100)
         |> assign(:loading_message, "Repository loading complete")}

      %{"status" => "ok", "repositories" => repositories} ->
        # Handle legacy format for backward compatibility
        {:ok,
         socket
         |> assign(assigns)
         |> assign(:repositories, repositories)
         |> assign(:loading_repositories, false)
         |> assign(:loading_progress, 100)
         |> assign(:loading_message, "Repository loading complete")}

      %{"status" => "error", "reason" => reason} ->
        require Logger
        Logger.error("Repository loading failed: #{inspect(reason)}")

        {:ok,
         socket
         |> assign(assigns)
         |> assign(:loading_repositories, false)
         |> assign(:loading_error, reason)
         |> put_flash(:error, "Failed to load repositories: #{reason}")}

      # Handle legacy tuple format for backward compatibility
      {:ok, repositories} ->
        {:ok,
         socket
         |> assign(assigns)
         |> assign(:repositories, repositories)
         |> assign(:loading_repositories, false)
         |> assign(:loading_progress, 100)
         |> assign(:loading_message, "Repository loading complete")}

      {:error, reason} ->
        {:ok,
         socket
         |> assign(assigns)
         |> assign(:loading_repositories, false)
         |> assign(:loading_error, reason)
         |> put_flash(:error, "Failed to load repositories: #{reason}")}
    end
  end

  def update(assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign_new(:selection_mode, fn -> :create end)
     |> assign_new(:template_repo, fn -> nil end)
     |> assign_new(:loading_repositories, fn -> false end)
     |> assign_new(:loading_progress, fn -> 0 end)
     |> assign_new(:loading_message, fn -> "" end)
     |> assign_new(:loading_error, fn -> nil end)
     |> maybe_load_repositories_async()}
  end

  def render(assigns) do
    ~H"""
    <div>
      <h2 class="text-2xl font-semibold text-slate-900 mb-4">Template Repository</h2>
      <div>
        <p class="text-slate-600 mb-6">
          First, let's create or select a template repository. This will be the foundation for managing your code standards and patterns.
          A template repository allows you to generate new repositories with the same directory structure, branches, and files.
        </p>

        <div class="space-y-6">
          <div>
            <label class="block text-sm font-medium leading-6 text-slate-900">
              Template Source
            </label>
            <fieldset class="mt-2">
              <legend class="sr-only">Select template source</legend>
              <div class="flex items-center space-x-10">
                <div class="flex items-center">
                  <input
                    id="create_new"
                    name="selection_mode"
                    type="radio"
                    value="create"
                    checked={@selection_mode == :create}
                    phx-click="change_selection_mode"
                    phx-value-mode="create"
                    phx-target={@myself}
                    class="radio"
                  />
                  <label
                    for="create_new"
                    class="ml-3 block text-sm font-medium leading-6 text-slate-900"
                  >
                    Create New Repository
                  </label>
                </div>
                <div class="flex items-center">
                  <input
                    id="select_existing"
                    name="selection_mode"
                    type="radio"
                    value="select"
                    checked={@selection_mode == :select}
                    phx-click="change_selection_mode"
                    phx-value-mode="select"
                    phx-target={@myself}
                    class="radio"
                    disabled={Enum.empty?(@repositories) || @loading_repositories}
                  />
                  <label
                    for="select_existing"
                    class={[
                      "ml-3 block text-sm font-medium leading-6",
                      (Enum.empty?(@repositories) || @loading_repositories) &&
                        "text-slate-400 cursor-not-allowed",
                      !Enum.empty?(@repositories) && !@loading_repositories && "text-slate-900"
                    ]}
                  >
                    Select Existing Repository
                  </label>
                </div>
              </div>
              <%= if @loading_repositories do %>
                <div class="mt-2 space-y-2">
                  <div class="flex items-center space-x-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
                    <p class="text-xs text-slate-600">{@loading_message}</p>
                  </div>
                  <div class="w-full bg-slate-200 rounded-full h-2">
                    <div
                      class="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                      style={"width: #{@loading_progress}%"}
                    >
                    </div>
                  </div>
                </div>
              <% else %>
                <%= if Enum.empty?(@repositories) do %>
                  <p class="mt-2 text-xs text-slate-500">
                    <%= if @loading_error do %>
                      Failed to load repositories. Please try refreshing the page.
                    <% else %>
                      You don't have any existing repositories eligible to be used as a template.
                    <% end %>
                  </p>
                <% end %>
              <% end %>
            </fieldset>
          </div>
          <.live_component
            :if={@selection_mode == :create}
            module={TemplateRepository.Create}
            id="template-repository-create"
            current_user={@current_user}
            current_organization={@current_organization}
            private_repos={@current_organization.private_repos}
          />
          <.live_component
            :if={@selection_mode == :select}
            module={TemplateRepository.Select}
            id="template-repository-select"
            current_user={@current_user}
            current_organization={@current_organization}
            repositories={@repositories}
          />
        </div>
      </div>
    </div>
    """
  end

  def handle_event("change_selection_mode", %{"mode" => "create"}, socket) do
    {:noreply, assign(socket, :selection_mode, :create)}
  end

  def handle_event("change_selection_mode", %{"mode" => "select"}, socket) do
    {:noreply, assign(socket, :selection_mode, :select)}
  end
end
